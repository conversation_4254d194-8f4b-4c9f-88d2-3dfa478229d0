name: Release and publish trzsz-ssh
on:
  release:
    types: [released]
jobs:
  release-and-publish:
    name: Release and publish trzsz-ssh
    runs-on: ubuntu-latest
    steps:
      - name: Checkout trzsz-ssh
        uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.21"
      - name: <PERSON> GoReleaser
        uses: goreleaser/goreleaser-action@v6
        with:
          distribution: goreleaser
          version: "~> v1"
          args: release --clean --skip=publish
      - name: Upload Release Assets
        uses: softprops/action-gh-release@v2
        with:
          files: |
            dist/*.tar.gz
            dist/*.zip
            dist/*.rpm
            dist/*_checksums.txt
      - name: Publish rpm to Gemfury
        env:
          FURY_TOKEN: ${{ secrets.FURY_TOKEN }}
        run: |
          for filename in dist/tssh*.rpm; do
            curl -F package=@"$filename" https://{$FURY_TOKEN}@push.fury.io/trzsz/
          done
  release-win7-patch:
    name: Build win7 release with go1.21 patch
    runs-on: ubuntu-latest
    steps:
      - name: Checkout trzsz-ssh
        uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.21"
      - name: Revert Go1.21
        run: |
          cd $(go env GOROOT)
          curl https://github.com/golang/go/commit/9e43850a3298a9b8b1162ba0033d4c53f8637571.diff | patch --verbose -R -p 1
      - name: Build trzsz-ssh
        run: |
          version=${GITHUB_REF/refs\/tags\/v/}
          GOOS=windows GOARCH=386 go build -ldflags "-s -w" -o tssh_${version}_win7_i386/ ./cmd/tssh/
          zip -r tssh_${version}_win7_i386.zip tssh_${version}_win7_i386
          GOOS=windows GOARCH=amd64 go build -ldflags "-s -w" -o tssh_${version}_win7_x86_64/ ./cmd/tssh/
          zip -r tssh_${version}_win7_x86_64.zip tssh_${version}_win7_x86_64
          sha256sum ./*.zip
      - name: Upload Release Assets
        uses: softprops/action-gh-release@v2
        with:
          files: ./*.zip
