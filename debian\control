Source: tssh
Section: utils
Priority: optional
Maintainer: <PERSON><PERSON> <<EMAIL>>
Build-Depends: debhelper (>=9),
               golang-1.21-go
Standards-Version: 3.9.6
Homepage: https://trzsz.github.io/ssh
Vcs-Browser: https://github.com/trzsz/trzsz-ssh
Vcs-Git: https://github.com/trzsz/trzsz-ssh.git

Package: tssh
Architecture: any
Depends: ${shlibs:Depends}, ${misc:Depends}
Description: simple ssh client with trzsz ( trz / tsz ) support.
