name: Go test trzsz-ssh
on: [push]
jobs:
  go-test-on-linux:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout trzsz-ssh
        uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.21"
      - name: go test
        run: go test -v -count=1 ./tssh
  go-test-on-macos:
    runs-on: macos-latest
    steps:
      - name: Checkout trzsz-ssh
        uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.21"
      - name: go test
        run: go test -v -count=1 ./tssh
  go-test-on-windows:
    runs-on: windows-latest
    steps:
      - name: Checkout trzsz-ssh
        uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.21"
      - name: go test
        run: go test -v -count=1 ./tssh
  go-release-snapshot:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout trzsz-ssh
        uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.21"
      - name: <PERSON> GoReleaser
        uses: goreleaser/goreleaser-action@v6
        with:
          distribution: goreleaser
          version: "~> v1"
          args: release --clean --snapshot --skip=publish
  test-win7-patch:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout trzsz-ssh
        uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.21"
      - name: Revert Go1.21
        run: |
          cd $(go env GOROOT)
          curl https://github.com/golang/go/commit/9e43850a3298a9b8b1162ba0033d4c53f8637571.diff | patch --verbose -R -p 1
      - name: Build trzsz-ssh
        run: |
          go test -v -count=1 ./tssh
          GOOS=windows GOARCH=386 go build -o tssh_win7_i386/ ./cmd/tssh/
          GOOS=windows GOARCH=amd64 go build -o tssh_win7_x86_64/ ./cmd/tssh/
