project_name: tssh
before:
  hooks:
    - go mod tidy
builds:
  - id: tssh
    main: ./cmd/tssh
    binary: tssh
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
      - android
      - freebsd
    goarch:
      - "386"
      - amd64
      - arm
      - arm64
      - loong64
    goarm:
      - "6"
      - "7"
    ignore:
      - goos: windows
        goarch: arm
      - goos: darwin
        goarch: arm
      - goos: android
        goarch: arm
      - goos: android
        goarch: "386"
      - goos: android
        goarch: amd64
      - goos: freebsd
        goarch: arm
      - goos: freebsd
        goarch: "386"
archives:
  - id: tssh
    name_template: >-
      {{ .ProjectName }}_
      {{- .Version }}_
      {{- if eq .Os "darwin" }}macos_
      {{- else }}{{ .Os }}_{{ end }}
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else if eq .Arch "arm64" }}aarch64
      {{- else if eq .<PERSON> "arm" }}armv{{ .Arm }}
      {{- else }}{{ .<PERSON> }}{{ end }}
    wrap_in_directory: true
    format_overrides:
      - goos: windows
        format: zip
    files:
      - none*
nfpms:
  - id: tssh
    builds:
      - tssh
    file_name_template: >-
      {{ .ProjectName }}_
      {{- .Version }}_
      {{- if eq .Os "darwin" }}macos_
      {{- else }}{{ .Os }}_{{ end }}
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else if eq .Arch "arm64" }}aarch64
      {{- else if eq .Arch "arm" }}armv{{ .Arm }}
      {{- else }}{{ .Arch }}{{ end }}
    homepage: https://trzsz.github.io/
    maintainer: Lonny Wong <<EMAIL>>
    description: |-
      tssh is a simple ssh client with trzsz ( trz / tsz ) support.
    license: MIT
    formats:
      - rpm
    bindir: /usr/bin
    rpm:
      group: Unspecified
snapshot:
  name_template: "{{ .Version }}.next"
checksum:
  name_template: "{{ .ProjectName }}_{{ .Version }}_checksums.txt"
